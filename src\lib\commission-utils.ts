/**
 * Utility functions for therapist commission calculations
 * Ensures consistent commission calculation across the application
 */

export interface ServiceCommissionData {
  serviceId: string;
  quantity: number;
  defaultCommission: number;
  specialCommission?: number;
}

export interface TherapistSpecialCommission {
  serviceId: string;
  commission: number;
}

/**
 * Calculate therapist commission for a list of services
 * @param services Array of services with commission data
 * @param therapistSpecialCommissions Array of special commissions for the therapist
 * @returns Total commission amount
 */
export function calculateTherapistCommission(
  services: ServiceCommissionData[],
  therapistSpecialCommissions: TherapistSpecialCommission[] = []
): number {
  let totalCommission = 0;

  // Create a map for quick lookup of special commissions
  const specialCommissionMap = new Map<string, number>();
  therapistSpecialCommissions.forEach(sc => {
    specialCommissionMap.set(sc.serviceId, sc.commission);
  });

  services.forEach(service => {
    // Check if there's a special commission for this service
    const hasSpecialCommission = specialCommissionMap.has(service.serviceId);
    const commissionPerItem = hasSpecialCommission
      ? specialCommissionMap.get(service.serviceId)!
      : service.defaultCommission;

    // Calculate commission: commission per item * quantity
    const serviceCommission = commissionPerItem * service.quantity;
    totalCommission += serviceCommission;

    console.log(`Service ${service.serviceId}: ${commissionPerItem} x ${service.quantity} = ${serviceCommission}`);
  });

  return totalCommission;
}

/**
 * Calculate therapist sales total (original service price + additional charges, excluding discounts)
 * @param subtotal Original service subtotal
 * @param additionalCharge Additional charges
 * @param discountAmount Discount amount (should NOT affect therapist income)
 * @returns Therapist sales total
 */
export function calculateTherapistSalesTotal(
  subtotal: number,
  additionalCharge: number = 0,
  discountAmount: number = 0
): number {
  // Therapist income = subtotal + additional charges
  // Discounts should NOT reduce therapist income
  return subtotal + additionalCharge;
}

/**
 * Validate commission calculation consistency
 * @param calculatedCommission Commission calculated from services
 * @param storedCommission Commission stored in database
 * @param tolerance Tolerance for floating point comparison
 * @returns Whether the values are consistent
 */
export function validateCommissionConsistency(
  calculatedCommission: number,
  storedCommission: number,
  tolerance: number = 0.01
): boolean {
  return Math.abs(calculatedCommission - storedCommission) <= tolerance;
}

/**
 * Get commission per item for a specific service and therapist
 * @param serviceId Service ID
 * @param defaultCommission Default commission from service
 * @param therapistSpecialCommissions Array of special commissions for the therapist
 * @returns Commission per item
 */
export function getCommissionPerItem(
  serviceId: string,
  defaultCommission: number,
  therapistSpecialCommissions: TherapistSpecialCommission[] = []
): number {
  const specialCommission = therapistSpecialCommissions.find(
    sc => sc.serviceId === serviceId
  );
  
  return specialCommission ? specialCommission.commission : defaultCommission;
}

/**
 * Format commission data for consistent logging
 * @param serviceId Service ID
 * @param commissionPerItem Commission per item
 * @param quantity Quantity
 * @param isSpecial Whether this is a special commission
 * @returns Formatted log string
 */
export function formatCommissionLog(
  serviceId: string,
  commissionPerItem: number,
  quantity: number,
  isSpecial: boolean = false
): string {
  const total = commissionPerItem * quantity;
  const type = isSpecial ? 'special' : 'default';
  return `Service ${serviceId}: ${commissionPerItem} (${type}) x ${quantity} = ${total}`;
}
