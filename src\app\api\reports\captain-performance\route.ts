import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Definisikan tipe yang dibutuhkan untuk menghindari penggunaan 'any'
type TransactionWhere = {
  transactionDate: {
    gte: Date;
    lte: Date;
  };
  outletId?: string;
  paymentStatus: 'PAID'; // Gunakan literal type untuk paymentStatus
};

// Tipe untuk item transaksi
interface TransactionItem {
  id: string | number;
  totalAmount: number;
  therapistId: string;
  therapistCommissionEarned: number | null;
  transactionDate: Date;
  outletId: string;
  discountType?: string;
  discountValue?: number;
  discountAmount?: number;
  additionalCharge?: number;
  therapist: {
    id: string;
    name: string;
    outletId: string;
    outlet: {
      id: string;
      name: string;
    };
  };
  originalTotalAmount?: number; // Untuk debugging
  transactionItems?: Array<TransactionItemDetail>;
}

// Tipe untuk detail item transaksi
interface TransactionItemDetail {
  id: string | number;
  price: number;
  quantity: number;
  service: {
    id: string;
    name: string;
    commission: number;
  };
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const outletId = searchParams.get('outletId');

    // Ambil parameter tanggal dari query
    const rawStartDate = searchParams.get('startDate');
    const rawEndDate = searchParams.get('endDate');

    if (!rawStartDate || !rawEndDate) {
      throw new Error('Tanggal mulai dan tanggal akhir harus disediakan');
    }

    // Parse tanggal
    const startDate = new Date(rawStartDate);
    const endDate = new Date(rawEndDate);

    // Validasi tanggal
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      throw new Error('Format tanggal tidak valid');
    }

    // Set waktu untuk tanggal mulai ke 00:00:00
    startDate.setHours(0, 0, 0, 0);

    // Set waktu untuk tanggal akhir ke 23:59:59
    endDate.setHours(23, 59, 59, 999);

    // Set tanggal untuk digunakan dalam query

    // Filter dasar untuk transaksi berdasarkan tanggal dan outlet terpilih
    const transactionWhere: TransactionWhere = {
      transactionDate: { gte: startDate, lte: endDate },
      ...(outletId && { outletId: outletId }),
      paymentStatus: 'PAID', // Hanya transaksi yang sudah dibayar
    };

    // 1. Ambil semua user yang merupakan kapten (isCaptain = true)
    const captains = await prisma.user.findMany({
      where: {
        isCaptain: true,
        role: 'STAFF',
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        username: true,
        therapistTeam: {
          where: {
            isActive: true, // Hanya ambil terapis yang aktif
          },
          select: {
            id: true,
            name: true,
            outletId: true,
            isActive: true,
            outlet: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // 2. Untuk setiap kapten, ambil data transaksi dari terapis di timnya
    const captainPerformanceData = await Promise.all(
      captains.map(async (captain) => {
        // Ambil ID semua terapis di tim kapten
        const therapistIds = captain.therapistTeam.map((therapist) => therapist.id);

        // Jika tidak ada terapis di tim, kembalikan data kosong
        if (therapistIds.length === 0) {
          return {
            captainId: captain.id,
            captainName: captain.name,
            captainUsername: captain.username,
            teamSize: 0,
            totalSales: 0,
            totalCommission: 0,
            discountAmount: 0,
            additionalCharge: 0,
            therapists: [],
          };
        }

        try {
          // Ambil data terapis dengan komisi khusus mereka
          const therapistsWithCommissions = await prisma.therapist.findMany({
            where: {
              id: { in: therapistIds },
            },
            select: {
              id: true,
              serviceCommissions: {
                include: {
                  service: true
                }
              }
            }
          });

          // Buat map untuk komisi khusus terapis
          const therapistCommissionMap = new Map();
          therapistsWithCommissions.forEach(therapist => {
            const commissionMap = new Map();
            therapist.serviceCommissions.forEach(sc => {
              commissionMap.set(sc.serviceId, sc.commission);
            });
            therapistCommissionMap.set(therapist.id, commissionMap);
          });

          // Ambil transaksi untuk terapis di tim kapten
          const therapistTransactions = await prisma.transaction.findMany({
            where: {
              ...transactionWhere,
              therapistId: {
                in: therapistIds,
              },
            },
            select: {
              id: true,
              totalAmount: true,
              therapistId: true,
              therapistCommissionEarned: true,
              transactionDate: true,
              outletId: true,
              discountType: true,
              discountValue: true,
              discountAmount: true,
              additionalCharge: true,
              therapist: {
                select: {
                  id: true,
                  name: true,
                  outletId: true,
                  outlet: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
              transactionItems: {
                select: {
                  id: true,
                  price: true,
                  quantity: true,
                  service: {
                    select: {
                      id: true,
                      name: true,
                      commission: true,
                    },
                  },
                },
              },
            },
          });

          // Proses transaksi yang ditemukan

          // Kelompokkan transaksi berdasarkan terapis ID
          const transactionsByTherapist = new Map<string, TransactionItem[]>();

          // Lakukan pengecekan data transaksi untuk mencegah duplikasi dan memastikan integritas data
          therapistTransactions.forEach(trx => {
            if (trx.therapistId) {
              if (!transactionsByTherapist.has(trx.therapistId)) {
                transactionsByTherapist.set(trx.therapistId, []);
              }

              // Check untuk memastikan transaksi belum ada (mencegah duplikasi)
              const existingTransaction = transactionsByTherapist.get(trx.therapistId)?.find((t: TransactionItem) => String(t.id) === String(trx.id));
              if (!existingTransaction) {
                // Validasi data transaksi
                // Pastikan nilai totalAmount masuk akal
                if (trx.totalAmount < 0) {
                  // Koreksi nilai negatif menjadi 0
                  trx.totalAmount = 0;
                }

                // Tambahkan transaksi ke daftar
                transactionsByTherapist.get(trx.therapistId)?.push({
                  id: trx.id,
                  totalAmount: trx.totalAmount,
                  therapistId: trx.therapistId,
                  therapistCommissionEarned: trx.therapistCommissionEarned,
                  transactionDate: trx.transactionDate,
                  outletId: trx.outletId,
                  therapist: trx.therapist,
                  originalTotalAmount: trx.totalAmount
                });
              } // Lewati transaksi duplikat
            }
          });

          // Hitung total penjualan dan komisi untuk setiap terapis
          const therapistPerformance = therapistIds.map((therapistId) => {
            const therapist = captain.therapistTeam.find((t) => t.id === therapistId);

            // Ambil transaksi terapis dari map yang sudah disiapkan
            const therapistTrx = transactionsByTherapist.get(therapistId) || [];

            // Filter transaksi hanya dalam rentang tanggal yang diminta
            const validTransactions = therapistTrx.filter(trx => {
              const trxDate = new Date(trx.transactionDate);
              return trxDate >= startDate && trxDate <= endDate;
            });

            // Hitung total penjualan dengan pengecekan ketat
            let totalSales = 0;
            const countedTransactionIds = new Set<string>();

            validTransactions.forEach((trx: TransactionItem) => {
              // Konversi ID transaksi ke string untuk keseragaman
              const trxId = String(trx.id);

              // Cek apakah transaksi sudah dihitung
              if (countedTransactionIds.has(trxId)) {
                console.log(`[API] Transaksi ${trxId} sudah dihitung sebelumnya, dilewati.`);
                return;
              }

              // Jika outletId spesifik diberikan, hanya hitung transaksi dari outlet tersebut
              if (outletId && trx.outletId !== outletId) {
                console.log(`[API] Transaksi ${trxId} dari outlet ${trx.outletId} dilewati karena filter outlet ${outletId}`);
                return;
              }

              totalSales += trx.totalAmount;
              countedTransactionIds.add(trxId);


            });

            // Hitung total komisi dengan cara yang sama seperti di endpoint therapists/commissions
            // Definisikan interface untuk service item (sama seperti di therapists/commissions)
            interface ServiceItem {
              name: string;
              price: number;
              commission: number;
              quantity: number;
            }

            // Definisikan interface untuk transaction group (sama seperti di therapists/commissions)
            interface TransactionGroup {
              transactionId: string;
              transactionDate: Date;
              customerName: string;
              outletName: string;
              services: ServiceItem[];
              totalAmount: number;
              calculatedCommission: number;
              therapistCommissionEarned: number;
              discountType?: string;
              discountValue?: number;
              discountAmount?: number;
              additionalCharge?: number;
            }

            // Buat Map untuk menyimpan grup transaksi (sama seperti di therapists/commissions)
            const transactionGroups = new Map<string, TransactionGroup>();

            // Proses setiap transaksi valid (sama seperti di therapists/commissions)
            validTransactions.forEach((trx: TransactionItem) => {
              const trxId = String(trx.id);

              // Jika outletId spesifik diberikan, hanya proses transaksi dari outlet tersebut
              if (outletId && trx.outletId !== outletId) {
                return;
              }

              // Gunakan therapistCommissionEarned dari transaksi sebagai nilai komisi utama
              // PERBAIKAN: Jangan ubah null menjadi 0, biarkan null untuk logic prioritas yang benar
              const therapistCommissionEarned = trx.therapistCommissionEarned;

              // Ambil nilai diskon dan biaya tambahan dari transaksi jika ada
              const discountType = trx.discountType || 'none';
              const discountValue = trx.discountValue || 0;
              const discountAmount = trx.discountAmount || 0;
              const additionalCharge = trx.additionalCharge || 0;

              // Inisialisasi grup transaksi baru
              if (!transactionGroups.has(trxId)) {
                transactionGroups.set(trxId, {
                  transactionId: trxId,
                  transactionDate: trx.transactionDate,
                  customerName: 'Pelanggan', // Tidak ada data customer di TransactionItem
                  outletName: trx.therapist.outlet.name,
                  services: [],
                  totalAmount: trx.totalAmount,
                  calculatedCommission: 0,
                  therapistCommissionEarned: therapistCommissionEarned,
                  discountType,
                  discountValue,
                  discountAmount,
                  additionalCharge,
                });
              }

              // Tambahkan layanan ke grup transaksi
              const group = transactionGroups.get(trxId)!;

              // Proses setiap item transaksi jika ada
              if (trx.transactionItems && trx.transactionItems.length > 0) {
                let calculatedCommission = 0; // Untuk menghitung komisi berdasarkan item

                trx.transactionItems.forEach((item: TransactionItemDetail) => {
                  if (item.service) {
                    const itemQuantity = item.quantity || 1;
                    
                    // PERBAIKAN: Cek apakah ada komisi khusus untuk terapis ini dan layanan ini
                    const therapistSpecialCommissions = therapistCommissionMap.get(trx.therapistId);
                    let commissionPerItem = 0;

                    if (therapistSpecialCommissions && therapistSpecialCommissions.has(item.service.id)) {
                      // Jika ada komisi khusus, gunakan nilai tersebut
                      commissionPerItem = therapistSpecialCommissions.get(item.service.id);
                    } else {
                      // Jika tidak ada komisi khusus, gunakan komisi default dari layanan
                      commissionPerItem = item.service.commission || 0;
                    }

                    // Cek apakah layanan dengan nama yang sama sudah ada
                    const existingServiceIndex = group.services.findIndex(s => s.name === item.service.name);

                    if (existingServiceIndex !== -1) {
                      // Jika layanan sudah ada, tambahkan quantity
                      group.services[existingServiceIndex].quantity += itemQuantity;
                      // PERBAIKAN: Tambahkan komisi untuk quantity tambahan ini
                      calculatedCommission += commissionPerItem * itemQuantity;
                    } else {
                      // Jika layanan belum ada, tambahkan sebagai layanan baru
                      group.services.push({
                        name: item.service.name,
                        price: item.price,
                        commission: commissionPerItem,
                        quantity: itemQuantity
                      });

                      // Tambahkan ke total komisi yang dihitung
                      calculatedCommission += commissionPerItem * itemQuantity;
                    }
                  }
                });

                // Simpan komisi yang dihitung dari item-item
                group.calculatedCommission = calculatedCommission;
              }
            });

            // Hitung total komisi (prioritaskan nilai dari database, fallback ke nilai yang dihitung)
            // PERBAIKAN: Gunakan logika yang sama dengan endpoint therapists/commissions
            const totalCommission = Array.from(transactionGroups.values()).reduce(
              (sum, group) => {
                const commissionValue = group.therapistCommissionEarned !== null && group.therapistCommissionEarned !== undefined
                  ? group.therapistCommissionEarned
                  : group.calculatedCommission;
                return sum + commissionValue;
              },
              0
            );



            // Hitung total diskon dan biaya tambahan
            const totalDiscountAmount = Array.from(transactionGroups.values()).reduce(
              (sum, group) => sum + (group.discountAmount || 0),
              0
            );

            const totalAdditionalCharge = Array.from(transactionGroups.values()).reduce(
              (sum, group) => sum + (group.additionalCharge || 0),
              0
            );

            return {
              therapistId,
              therapistName: therapist?.name || 'Unknown Therapist',
              outletId: therapist?.outletId || '',
              outletName: therapist?.outlet?.name || 'Unknown Outlet',
              totalSales,
              totalCommission,
              transactionCount: countedTransactionIds.size,
              discountAmount: totalDiscountAmount,
              additionalCharge: totalAdditionalCharge,
            };
          });

          // Filter terapis yang memiliki transaksi
          const activeTherapists = therapistPerformance.filter(t => t.transactionCount > 0);

          // Urutkan terapis berdasarkan total penjualan (dari tertinggi ke terendah)
          activeTherapists.sort((a, b) => b.totalSales - a.totalSales);

          // Hitung total untuk kapten
          const totalSales = activeTherapists.reduce((sum, t) => sum + t.totalSales, 0);
          const totalCommission = activeTherapists.reduce((sum, t) => sum + t.totalCommission, 0);
          const totalDiscountAmount = activeTherapists.reduce((sum, t) => sum + (t.discountAmount || 0), 0);
          const totalAdditionalCharge = activeTherapists.reduce((sum, t) => sum + (t.additionalCharge || 0), 0);


          // Hitung jumlah terapis aktif yang memiliki transaksi
          const activeTherapistCount = activeTherapists.length;

          return {
            captainId: captain.id,
            captainName: captain.name,
            captainUsername: captain.username,
            teamSize: activeTherapistCount, // Gunakan jumlah terapis aktif yang memiliki transaksi
            totalSales,
            totalCommission,
            discountAmount: totalDiscountAmount,
            additionalCharge: totalAdditionalCharge,
            therapists: activeTherapists,
          };
        } catch (err) {
          // Tangani error saat memproses data kapten
          return {
            captainId: captain.id,
            captainName: captain.name,
            captainUsername: captain.username,
            teamSize: 0,
            totalSales: 0,
            totalCommission: 0,
            discountAmount: 0,
            additionalCharge: 0,
            therapists: [],
            error: String(err)
          };
        }
      })
    );

    // Filter kapten yang memiliki tim dengan transaksi
    const activeCaptains = captainPerformanceData.filter(
      (captain) => captain.therapists && captain.therapists.length > 0
    );

    // Urutkan berdasarkan total penjualan (dari tertinggi ke terendah)
    activeCaptains.sort((a, b) => b.totalSales - a.totalSales);

    return NextResponse.json({
      message: 'Data kinerja kapten berhasil diambil',
      captainPerformance: activeCaptains,
      filters: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        outletId: outletId,
      },
    });
  } catch (error: unknown) {
    // Tangani error pada level API

    let errorMessage = 'Terjadi kesalahan saat mengambil data kinerja kapten';
    const statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return NextResponse.json({
      error: errorMessage,
      message: 'Terjadi kesalahan saat mengambil data kinerja kapten',
      captainPerformance: [],
      filters: {
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        outletId: null,
      },
    }, { status: statusCode });
  }
}
